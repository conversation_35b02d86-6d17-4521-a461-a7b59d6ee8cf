"use client";
import { createContext, useContext, useState, useEffect, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
const TimerContext = createContext();

// Configuration constants
const MODAL_TRIGGER_TIME = 7 * 60; // 1 minute in seconds (for testing)
const LOGOUT_GRACE_PERIOD = 60; // 60 seconds after modal shows
const THROTTLE_DELAY = 1000; // Throttle activity events to 1 second

// Storage keys
const LAST_ACTIVITY_KEY = "last_activity_time";
const SESSION_REFRESH_KEY = "session_refresh_allowed";

// Utility functions
const isClient = () => typeof window !== "undefined";

const safeStorageGet = (key, storage = 'localStorage') => {
  try {
    if (!isClient()) return null;
    return storage === 'localStorage' ? localStorage.getItem(key) : sessionStorage.getItem(key);
  } catch (error) {
    console.warn(`Failed to get ${key} from ${storage}:`, error);
    return null;
  }
};

const safeStorageSet = (key, value, storage = 'localStorage') => {
  try {
    if (!isClient()) return false;
    if (storage === 'localStorage') {
      localStorage.setItem(key, value);
    } else {
      sessionStorage.setItem(key, value);
    }
    return true;
  } catch (error) {
    console.warn(`Failed to set ${key} in ${storage}:`, error);
    return false;
  }
};

export const TimerContextProvider = ({ children }) => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const [sessionRefreshAllowed, setSessionRefreshAllowed] = useState(true);
  const [lastActivity, setLastActivity] = useState(null);
  const [isLogout, setIsLogout] = useState(false);

  // Timer refs for proper cleanup
  const inactivityTimerRef = useRef(null);
  const logoutTimerRef = useRef(null);
  const throttleTimerRef = useRef(null);

  // Throttled activity handler to prevent excessive calls
  const throttledActivityHandler = useCallback(() => {
    if (throttleTimerRef.current) return;

    throttleTimerRef.current = setTimeout(() => {
      throttleTimerRef.current = null;
      if (!isLogout) {
        resetInactivityTimer();
      }
    }, THROTTLE_DELAY);
  }, [isLogout]);

  // Clear all timers
  const clearAllTimers = useCallback(() => {
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
      inactivityTimerRef.current = null;
    }
    if (logoutTimerRef.current) {
      clearTimeout(logoutTimerRef.current);
      logoutTimerRef.current = null;
    }
    if (throttleTimerRef.current) {
      clearTimeout(throttleTimerRef.current);
      throttleTimerRef.current = null;
    }
  }, []);

  // Initialize inactivity tracking
  useEffect(() => {
    const initializeInactivityTimer = () => {
      const userToken = safeStorageGet("user", "sessionStorage");

      if (userToken) {
        const currentTime = Date.now();
        setLastActivity(currentTime);
        safeStorageSet(LAST_ACTIVITY_KEY, currentTime.toString());
        startInactivityTimer();
      } else {
        resetTimerState();
      }
    };

    if (isClient()) {
      initializeInactivityTimer();
    }

    // Cleanup on unmount
    return () => {
      clearAllTimers();
    };
  }, [clearAllTimers]);

  const startInactivityTimer = useCallback(() => {
    // Clear existing timers
    clearAllTimers();

    inactivityTimerRef.current = setTimeout(() => {
      if (!isLogout) {
        setShowModal(true);

        // Start logout timer
        logoutTimerRef.current = setTimeout(() => {
          if (!isLogout) {
            stopTimer();
          }
        }, LOGOUT_GRACE_PERIOD * 1000);
      }
    }, MODAL_TRIGGER_TIME * 1000);
  }, [isLogout, clearAllTimers]);

  const resetInactivityTimer = useCallback(() => {
    const currentTime = Date.now();
    setLastActivity(currentTime);
    safeStorageSet(LAST_ACTIVITY_KEY, currentTime.toString());
    setShowModal(false); // Hide modal if user becomes active
    startInactivityTimer();
  }, [startInactivityTimer]);

  // Activity event listeners with throttling
  useEffect(() => {
    const activityEvents = ['mousemove', 'keydown', 'click', 'scroll', 'touchstart'];

    const userToken = safeStorageGet("user", "sessionStorage");

    if (userToken && !isLogout && isClient()) {
      activityEvents.forEach(event => {
        window.addEventListener(event, throttledActivityHandler, {
          passive: true,
          capture: false
        });
      });
    }

    return () => {
      if (isClient()) {
        activityEvents.forEach(event => {
          window.removeEventListener(event, throttledActivityHandler);
        });
      }
      clearAllTimers();
    };
  }, [isLogout, throttledActivityHandler, clearAllTimers]);

  const startTimer = useCallback(() => {
    const currentTime = Date.now();
    setLastActivity(currentTime);
    safeStorageSet(LAST_ACTIVITY_KEY, currentTime.toString());
    safeStorageSet(SESSION_REFRESH_KEY, "true");
    setShowModal(false);
    setSessionRefreshAllowed(true);
    setIsLogout(false);
    startInactivityTimer();
  }, [startInactivityTimer]);

  const stopTimer = useCallback(() => {
    setIsLogout(true);
    clearAllTimers();

    toast.info("Session expired. Please log in again.");

    setTimeout(() => {
      if (isClient()) {
        try {
          localStorage.clear();
          sessionStorage.clear();
        } catch (error) {
          console.warn("Failed to clear storage:", error);
        }
        window.location.href = "/sign/login";
      }
    }, 1500);
  }, [clearAllTimers]);

  const handleSessionContinue = useCallback(async () => {
    try {
      if (!isClient()) return;

      const refreshToken = safeStorageGet("refreshToken");
      if (!refreshToken) {
        throw new Error("No refresh token available");
      }

      const response = await axios({
        url: `${Base_url}/get-access-token/?refresh_token=${refreshToken}`,
        method: "GET",
        timeout: 10000, // 10 second timeout
      });

      const newAccessToken = response.data?.data?.access_token;
      if (!newAccessToken) {
        throw new Error("Invalid response format");
      }

      safeStorageSet("user", newAccessToken, "sessionStorage");
      startTimer();
      toast.success("Session refreshed!");
    } catch (error) {
      console.error("Session refresh failed:", error);
      toast.error("Could not refresh session. Please log in again.");
      stopTimer();
    }
  }, [startTimer, stopTimer]);

  const resetTimerState = useCallback(() => {
    setLastActivity(null);
    setShowModal(false);
    setSessionRefreshAllowed(false);
    clearAllTimers();
  }, [clearAllTimers]);

  return (
    <TimerContext.Provider
      value={{
        showModal,
        sessionRefreshAllowed,
        handleSessionContinue,
        handleSessionEnd: stopTimer,
        startTimer,
        stopTimer,
      }}
    >
      {children}
    </TimerContext.Provider>
  );
};

export const useTimer = () => {
  const context = useContext(TimerContext);
  if (!context) {
    throw new Error("useTimer must be used within TimerContextProvider");
  }
  return context;
};
