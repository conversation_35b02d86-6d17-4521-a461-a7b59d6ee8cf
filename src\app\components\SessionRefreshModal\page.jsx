"use client";
import React, { useState, useEffect } from "react";
import styles from "./pop.module.css";

const SessionModal = ({ onContinue, onCancel }) => {
  const [timeLeft, setTimeLeft] = useState(60); // 60 seconds = 1 minute

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          onCancel(); // Auto-cancel when timer reaches 0
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [onCancel]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <div className={styles.cont}>
      <div className={styles.heading}>Inactivity Detected</div>
      <div className={styles.boxBody}>
        <div className={styles.timer}>{formatTime(timeLeft)}</div>
        <p>You've been inactive for 7 minutes. Continue your session?</p>
        <div className={styles.btnContainer}>
          <button className={styles.btnGreen} onClick={onContinue}>
            Continue Session
          </button>
          <button className={styles.btnRed} onClick={onCancel}>
            End Session
          </button>
        </div>
      </div>
    </div>
  );
};

export default SessionModal;
